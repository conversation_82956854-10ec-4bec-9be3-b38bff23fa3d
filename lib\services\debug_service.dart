import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class DebugService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Test notification system
  static Future<void> testNotificationSystem() async {
    debugPrint('🔧 === NOTIFICATION SYSTEM DEBUG TEST ===');
    
    final user = _auth.currentUser;
    if (user == null) {
      debugPrint('❌ No authenticated user found');
      return;
    }

    debugPrint('✅ Current user: ${user.uid} (${user.email})');

    try {
      // Test 1: Check if user document exists
      debugPrint('\n📋 Test 1: Checking user document...');
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data()!;
        debugPrint('✅ User document exists');
        debugPrint('📝 User data: ${userData.keys.toList()}');
        debugPrint('🔔 FCM Token: ${userData['fcmToken'] ?? 'NOT SET'}');
      } else {
        debugPrint('❌ User document does not exist');
      }

      // Test 2: Check notifications collection structure
      debugPrint('\n📋 Test 2: Checking notifications collection...');
      final notificationsRef = _firestore.collection('notifications').doc(user.uid).collection('items');
      final notificationsSnapshot = await notificationsRef.limit(5).get();
      
      debugPrint('📊 Total notifications: ${notificationsSnapshot.docs.length}');
      
      if (notificationsSnapshot.docs.isNotEmpty) {
        debugPrint('📝 Sample notification structure:');
        final sampleNotification = notificationsSnapshot.docs.first.data();
        sampleNotification.forEach((key, value) {
          debugPrint('   $key: $value (${value.runtimeType})');
        });
      } else {
        debugPrint('⚠️ No notifications found');
      }

      // Test 3: Create a test notification
      debugPrint('\n📋 Test 3: Creating test notification...');
      final testNotificationData = {
        'title': 'Debug Test Notification',
        'body': 'This is a test notification created at ${DateTime.now()}',
        'type': 'debug',
        'timestamp': FieldValue.serverTimestamp(),
        'senderId': user.uid,
        'read': false,
        'debugTest': true,
      };

      await notificationsRef.add(testNotificationData);
      debugPrint('✅ Test notification created successfully');

      // Test 4: Verify the notification was created
      debugPrint('\n📋 Test 4: Verifying test notification...');
      await Future.delayed(const Duration(seconds: 2)); // Wait for server timestamp
      
      final verifySnapshot = await notificationsRef
          .where('debugTest', isEqualTo: true)
          .orderBy('timestamp', descending: true)
          .limit(1)
          .get();

      if (verifySnapshot.docs.isNotEmpty) {
        debugPrint('✅ Test notification verified in database');
        final verifiedData = verifySnapshot.docs.first.data();
        debugPrint('📝 Verified data: $verifiedData');
      } else {
        debugPrint('❌ Test notification not found in database');
      }

    } catch (e) {
      debugPrint('❌ Error during notification test: $e');
    }

    debugPrint('\n🔧 === NOTIFICATION DEBUG TEST COMPLETE ===');
  }

  // Test sessions collection
  static Future<void> testSessionsCollection() async {
    debugPrint('🔧 === SESSIONS COLLECTION DEBUG TEST ===');
    
    final user = _auth.currentUser;
    if (user == null) {
      debugPrint('❌ No authenticated user found');
      return;
    }

    try {
      final sessionsSnapshot = await _firestore.collection('sessions').limit(10).get();
      debugPrint('📊 Total sessions in database: ${sessionsSnapshot.docs.length}');
      
      if (sessionsSnapshot.docs.isNotEmpty) {
        debugPrint('📝 Sample session structure:');
        final sampleSession = sessionsSnapshot.docs.first.data();
        sampleSession.forEach((key, value) {
          debugPrint('   $key: $value (${value.runtimeType})');
        });
      } else {
        debugPrint('⚠️ No sessions found');
      }
    } catch (e) {
      debugPrint('❌ Error testing sessions: $e');
    }

    debugPrint('🔧 === SESSIONS DEBUG TEST COMPLETE ===');
  }

  // Test messages/chats collection
  static Future<void> testMessagesCollection() async {
    debugPrint('🔧 === MESSAGES COLLECTION DEBUG TEST ===');
    
    final user = _auth.currentUser;
    if (user == null) {
      debugPrint('❌ No authenticated user found');
      return;
    }

    try {
      final chatsSnapshot = await _firestore.collection('chats').limit(10).get();
      debugPrint('📊 Total chats in database: ${chatsSnapshot.docs.length}');
      
      if (chatsSnapshot.docs.isNotEmpty) {
        debugPrint('📝 Sample chat structure:');
        final sampleChat = chatsSnapshot.docs.first.data();
        sampleChat.forEach((key, value) {
          debugPrint('   $key: $value (${value.runtimeType})');
        });
      } else {
        debugPrint('⚠️ No chats found');
      }
    } catch (e) {
      debugPrint('❌ Error testing messages: $e');
    }

    debugPrint('🔧 === MESSAGES DEBUG TEST COMPLETE ===');
  }

  // Clean up debug notifications
  static Future<void> cleanupDebugNotifications() async {
    debugPrint('🧹 Cleaning up debug notifications...');
    
    final user = _auth.currentUser;
    if (user == null) return;

    try {
      final debugNotifications = await _firestore
          .collection('notifications')
          .doc(user.uid)
          .collection('items')
          .where('debugTest', isEqualTo: true)
          .get();

      final batch = _firestore.batch();
      for (final doc in debugNotifications.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
      debugPrint('✅ Cleaned up ${debugNotifications.docs.length} debug notifications');
    } catch (e) {
      debugPrint('❌ Error cleaning up debug notifications: $e');
    }
  }

  // Run all tests
  static Future<void> runAllTests() async {
    debugPrint('🚀 === STARTING COMPREHENSIVE DEBUG TESTS ===');
    
    await testNotificationSystem();
    await Future.delayed(const Duration(seconds: 1));
    
    await testSessionsCollection();
    await Future.delayed(const Duration(seconds: 1));
    
    await testMessagesCollection();
    
    debugPrint('🚀 === ALL DEBUG TESTS COMPLETE ===');
  }
}
