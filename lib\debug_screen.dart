import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'services/debug_service.dart';
import 'services/notification_service.dart';

class DebugScreen extends StatefulWidget {
  const DebugScreen({super.key});

  @override
  State<DebugScreen> createState() => _DebugScreenState();
}

class _DebugScreenState extends State<DebugScreen> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  bool _isRunningTest = false;
  String _testResults = '';

  void _addToResults(String message) {
    setState(() {
      _testResults += '$message\n';
    });
  }

  Future<void> _runNotificationTest() async {
    setState(() {
      _isRunningTest = true;
      _testResults = '';
    });

    _addToResults('🔧 Starting notification system test...');
    
    try {
      await DebugService.testNotificationSystem();
      _addToResults('✅ Notification test completed');
    } catch (e) {
      _addToResults('❌ Notification test failed: $e');
    }

    setState(() {
      _isRunningTest = false;
    });
  }

  Future<void> _sendTestNotification() async {
    final user = _auth.currentUser;
    if (user == null) {
      _addToResults('❌ No user logged in');
      return;
    }

    setState(() {
      _isRunningTest = true;
    });

    try {
      _addToResults('📤 Sending test notification...');
      
      await NotificationService.sendNotificationToUser(
        userId: user.uid,
        title: 'Test Notification',
        body: 'This is a manual test notification sent at ${DateTime.now()}',
        type: 'test',
        data: {'testId': DateTime.now().millisecondsSinceEpoch.toString()},
      );
      
      _addToResults('✅ Test notification sent successfully');
    } catch (e) {
      _addToResults('❌ Failed to send test notification: $e');
    }

    setState(() {
      _isRunningTest = false;
    });
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunningTest = true;
      _testResults = '';
    });

    _addToResults('🚀 Running comprehensive tests...');
    
    try {
      await DebugService.runAllTests();
      _addToResults('✅ All tests completed');
    } catch (e) {
      _addToResults('❌ Tests failed: $e');
    }

    setState(() {
      _isRunningTest = false;
    });
  }

  Future<void> _cleanupDebugData() async {
    setState(() {
      _isRunningTest = true;
    });

    try {
      _addToResults('🧹 Cleaning up debug data...');
      await DebugService.cleanupDebugNotifications();
      _addToResults('✅ Debug data cleaned up');
    } catch (e) {
      _addToResults('❌ Cleanup failed: $e');
    }

    setState(() {
      _isRunningTest = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final user = _auth.currentUser;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug & Testing'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // User Info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current User',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('UID: ${user?.uid ?? 'Not logged in'}'),
                    Text('Email: ${user?.email ?? 'N/A'}'),
                    Text('Display Name: ${user?.displayName ?? 'N/A'}'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Test Buttons
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Debug Actions',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    ElevatedButton.icon(
                      onPressed: _isRunningTest ? null : _runNotificationTest,
                      icon: const Icon(Icons.notifications_active),
                      label: const Text('Test Notification System'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    ElevatedButton.icon(
                      onPressed: _isRunningTest ? null : _sendTestNotification,
                      icon: const Icon(Icons.send),
                      label: const Text('Send Test Notification'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    ElevatedButton.icon(
                      onPressed: _isRunningTest ? null : _runAllTests,
                      icon: const Icon(Icons.bug_report),
                      label: const Text('Run All Tests'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    ElevatedButton.icon(
                      onPressed: _isRunningTest ? null : _cleanupDebugData,
                      icon: const Icon(Icons.cleaning_services),
                      label: const Text('Cleanup Debug Data'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Results
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Test Results',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          if (_isRunningTest)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child: SingleChildScrollView(
                            child: Text(
                              _testResults.isEmpty ? 'No tests run yet...' : _testResults,
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
